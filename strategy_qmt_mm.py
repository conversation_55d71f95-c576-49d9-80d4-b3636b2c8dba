#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QMT做市商策略 - 完全对齐qmt_mm.py
集成AS模型、网格层数控制、动态参数调整等功能
"""

from dataclasses import dataclass
from typing import Dict, Any, Optional, Tuple, List
import pandas as pd
import numpy as np
import sys
import os

# 添加utils路径以导入AS模型
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

from strategy_base import StrategyBase, StrategyConfig, MarketData, Signal, strategy_manager


@dataclass
class QMTMMConfig(StrategyConfig):
    """QMT做市商策略配置"""
    # 基础参数
    enter_lot: int = 5000
    grid_spread: float = 0.006  # 0.6% 网格价差 (对齐qmt_mm.py)
    tp_spread: float = 0.004    # 0.4% 止盈价差 (对齐qmt_mm.py)
    sl_ratio: float = 2.0       # 2倍止损比例
    min_spread: float = 0.0015  # 最小价差
    max_holding_time_seconds: float = 3600.0  # 最大持仓时间（秒），超过则止损
    
    # AS模型参数
    risk_probility: float = 0.01
    risk_probility_buy: float = 0.05
    risk_probility_sell: float = 0.1
    intensity_window: int = 60  # 强度估计窗口(秒)
    
    # 网格参数
    max_grid_layers: int = 3
    grid_spreadnet_param: List[float] = None  # 网格价差参数
    grid_spreadqty_param: List[float] = None  # 网格数量参数
    
    # Entropy参数
    entropy_ratio_buy_threshold: float = 1.2
    entropy_ratio_sell_threshold: float = 0.8
    entropy_ema_alpha: float = 0.3
    ratio_ema_alpha: float = 0.2
    
    # 价格更新参数
    order_update_interval: int = 1000  # 毫秒
    spread_lifetime: int = 9000  # 价差生命周期(毫秒)
    
    # 其他参数
    max_position: int = 50000
    
    def __post_init__(self):
        if self.grid_spreadnet_param is None:
            self.grid_spreadnet_param = [1.0, 1.5, 2.0, 2.5]  # 对齐qmt_mm.py
        if self.grid_spreadqty_param is None:
            self.grid_spreadqty_param = [1.0, 1.0, 2.0, 2.0]  # 对齐qmt_mm.py


class SimpleIntensityEstimator:
    """简化的强度估计器 - 模拟AS模型"""
    
    def __init__(self, spread: float, spread_direction: float, dt: int):
        self.spread = spread
        self.spread_direction = spread_direction
        self.dt = dt
        self.intensity_estimates = []
        self.last_price = float('nan')
        self.last_update_time = 0
        
    def on_tick(self, ref_price: float, fill_price: float, ts: int, window_start: int):
        """更新强度估计"""
        if not np.isnan(self.last_price):
            # 简化的强度计算
            price_change = abs(fill_price - self.last_price)
            if price_change > 0:
                intensity = 1.0 / (price_change * 1000 + 1)  # 简化公式
                self.intensity_estimates.append(intensity)
                
                # 保持窗口大小
                max_estimates = 100
                if len(self.intensity_estimates) > max_estimates:
                    self.intensity_estimates = self.intensity_estimates[-max_estimates:]
        
        self.last_price = fill_price
        self.last_update_time = ts
    
    def estimate_ak(self, ts: int, window_start: int) -> np.ndarray:
        """估计A和k参数"""
        if len(self.intensity_estimates) < 2:
            return np.array([np.nan, np.nan])
        
        # 简化的A和k估计
        recent_estimates = self.intensity_estimates[-10:]  # 使用最近10个估计
        if len(recent_estimates) > 0:
            a_param = np.mean(recent_estimates) * 100  # 简化的A参数
            k_param = np.std(recent_estimates) * 10 + 0.1  # 简化的k参数
            return np.array([a_param, k_param])
        
        return np.array([np.nan, np.nan])


def get_spread(risk_prob: float, a_param: float, k_param: float) -> float:
    """计算AS模型价差"""
    if a_param <= 0 or k_param <= 0:
        return 0.003  # 默认价差
    
    # 简化的AS价差公式
    spread = risk_prob * np.sqrt(a_param / k_param) + 0.001
    return min(max(spread, 0.0015), 0.01)  # 限制在合理范围内


def dynamic_mid(ask_price: float, bid_price: float, ask_vol: float, bid_vol: float) -> float:
    """动态中间价计算"""
    if ask_vol + bid_vol == 0:
        return (ask_price + bid_price) / 2
    
    # 基于成交量加权的中间价
    total_vol = ask_vol + bid_vol
    return (bid_price * ask_vol + ask_price * bid_vol) / total_vol


def max_vol_spread(tick_data) -> float:
    """基于最大成交量的价差"""
    # 简化实现
    return 0.003


def round_price(price: float) -> float:
    """将价格精确到0.001"""
    return round(price * 1000) / 1000


class QMTMMStrategy(StrategyBase):
    """QMT做市商策略 - 完全对齐qmt_mm.py"""

    def __init__(self, config: QMTMMConfig):
        super().__init__(config)
        
        # 策略状态
        self.position_quantity = 0
        self.position_avg_price = 0.0
        self.internal_cost = -1.0  # 内部维护的成本价
        self.enter_price = 0.0
        
        # 网格状态
        self.grid_add_layer = 0
        self.reach_tp1 = False
        self.if_add_vol = False
        self.sell_enable = False
        self.position_start_time = None  # 持仓开始时间（毫秒）
        
        # 价格状态
        self.buy_price = 999.0
        self.sell_price = 0.0
        self.last_bid_spread = 0.003
        self.last_ask_spread = 0.003
        self.last_valid_time = 0
        self.last_order_update_time = 0
        
        # Entropy状态
        self.ask_entropy = 0.0
        self.bid_entropy = 0.0
        self.entropy_ratio = 1.0
        self.entropy_ratio_ema = 1.0
        self.bid_entropy_ema = 0.0
        self.ask_entropy_ema = 0.0
        
        # AS模型强度估计器
        self.buy_est = SimpleIntensityEstimator(-0.001, -1, 1000)
        self.sell_est = SimpleIntensityEstimator(0.001, 1, 1000)
        
        print("🤖 QMT做市商策略初始化完成")
    
    def initialize(self, historical_data: pd.DataFrame):
        """策略初始化"""
        print(f"🔄 QMT做市商策略初始化...")
        print(f"📊 历史数据: {len(historical_data)} 行")
        
        # 初始化强度估计器
        if len(historical_data) > 0:
            for _, row in historical_data.tail(50).iterrows():  # 使用最近50行初始化
                timestamp = row.get('timestamp', 0)
                mid_price = (row.get('bid_price', 0) + row.get('ask_price', 0)) / 2
                
                self.buy_est.on_tick(mid_price, row.get('bid_price', 0), timestamp, timestamp - 60000)
                self.sell_est.on_tick(mid_price, row.get('ask_price', 0), timestamp, timestamp - 60000)
        
        self.initialized = True
        print(f"✅ QMT做市商策略初始化完成")
    
    def calculate_entropy(self, data: MarketData, side: str) -> float:
        """计算熵因子"""
        if not hasattr(data, 'bid_volumes') or not hasattr(data, 'ask_volumes'):
            return 0.0
        
        if side == 'bid' and data.bid_volumes:
            volumes = np.array(data.bid_volumes[:5])  # 前5档
        elif side == 'ask' and data.ask_volumes:
            volumes = np.array(data.ask_volumes[:5])  # 前5档
        else:
            return 0.0
        
        # 计算熵
        volumes = volumes[volumes > 0]  # 过滤零成交量
        if len(volumes) == 0:
            return 0.0
        
        probs = volumes / np.sum(volumes)
        entropy = -np.sum(probs * np.log(probs + 1e-10))
        return entropy
    
    def update_entropy(self, data: MarketData):
        """更新熵指标"""
        ask_entropy = self.calculate_entropy(data, 'ask')
        bid_entropy = self.calculate_entropy(data, 'bid')
        
        # EMA更新
        alpha = self.config.entropy_ema_alpha
        self.ask_entropy_ema = alpha * ask_entropy + (1 - alpha) * self.ask_entropy_ema
        self.bid_entropy_ema = alpha * bid_entropy + (1 - alpha) * self.bid_entropy_ema
        
        # 计算熵比率
        if self.bid_entropy_ema > 0:
            self.entropy_ratio = self.ask_entropy_ema / self.bid_entropy_ema
        
        # EMA更新熵比率
        ratio_alpha = self.config.ratio_ema_alpha
        self.entropy_ratio_ema = ratio_alpha * self.entropy_ratio + (1 - ratio_alpha) * self.entropy_ratio_ema
        
        self.ask_entropy = ask_entropy
        self.bid_entropy = bid_entropy
    
    def get_entropy_trading_signal(self) -> Tuple[str, float]:
        """获取熵交易信号"""
        if self.entropy_ratio_ema > self.config.entropy_ratio_buy_threshold:
            return "buy", abs(self.entropy_ratio_ema - self.config.entropy_ratio_buy_threshold)
        elif self.entropy_ratio_ema < self.config.entropy_ratio_sell_threshold:
            return "sell", abs(self.config.entropy_ratio_sell_threshold - self.entropy_ratio_ema)
        else:
            return "hold", 0.0
    
    def should_update_prices(self, timestamp: int) -> bool:
        """判断是否应该更新价格"""
        return timestamp - self.last_order_update_time > self.config.order_update_interval
    
    def update_prices(self, data: MarketData):
        """更新买卖价格 - 集成AS模型"""
        # 检查价格数据有效性
        if pd.isna(data.ask_price) or pd.isna(data.bid_price) or pd.isna(data.ask_volume) or pd.isna(data.bid_volume):
            return  # 跳过无效数据

        mid_price = dynamic_mid(data.ask_price, data.bid_price, data.ask_volume, data.bid_volume)
        
        # 更新强度估计器
        self.buy_est.on_tick(mid_price, data.bid_price, data.timestamp, 
                           data.timestamp - self.config.intensity_window * 1000)
        self.sell_est.on_tick(mid_price, data.ask_price, data.timestamp,
                            data.timestamp - self.config.intensity_window * 1000)
        
        # 估计AS参数
        buy_ak = self.buy_est.estimate_ak(data.timestamp, 
                                        data.timestamp - self.config.intensity_window * 1000)
        sell_ak = self.sell_est.estimate_ak(data.timestamp,
                                          data.timestamp - self.config.intensity_window * 1000)
        
        # 计算价差
        bid_spread = 0.0
        ask_spread = 0.0
        
        if not np.any(np.isnan(buy_ak)) and buy_ak[0] * buy_ak[1] != 0:
            bid_spread = get_spread(self.config.risk_probility, buy_ak[0], buy_ak[1])
            bid_spread = max(bid_spread, self.config.min_spread)
        
        if not np.any(np.isnan(sell_ak)) and sell_ak[0] * sell_ak[1] != 0:
            ask_spread = get_spread(self.config.risk_probility, sell_ak[0], sell_ak[1])
        
        # 价差生命周期检查
        if data.timestamp - self.last_valid_time > self.config.spread_lifetime and bid_spread <= 0:
            bid_spread = 0.003
            ask_spread = 0.003
        elif data.timestamp - self.last_valid_time <= self.config.spread_lifetime and bid_spread <= 0:
            bid_spread = self.last_bid_spread
            ask_spread = self.last_ask_spread
        else:
            self.last_valid_time = data.timestamp
            
            # 限制价差范围
            maxvol_bid_spread = max_vol_spread(data)
            if bid_spread > 0.005:
                bid_spread = maxvol_bid_spread * 0.8 + bid_spread * 0.2
            elif bid_spread > maxvol_bid_spread:
                bid_spread = max(maxvol_bid_spread * 0.618 + bid_spread * 0.382, self.config.min_spread)
        
        # 根据熵信号调整价差
        market_type, strength = self.get_entropy_trading_signal()
        if market_type == "sell":
            bid_spread += 0.001
        elif market_type == "buy":
            bid_spread = max(bid_spread - 0.001, self.config.min_spread)
        
        # 更新价格
        self.buy_price = round((mid_price - bid_spread) * 1000) / 1000
        self.sell_price = round((data.last_price + ask_spread) * 1000) / 1000
        
        self.last_bid_spread = bid_spread
        self.last_ask_spread = ask_spread
        self.last_order_update_time = data.timestamp

    def on_market_data(self, data: MarketData) -> Optional[Signal]:
        """处理市场数据 - 完全对齐qmt_mm.py逻辑"""
        if not self.initialized:
            return None

        # 更新entropy指标
        self.update_entropy(data)

        # 更新价格
        market_type, strength = self.get_entropy_trading_signal()
        if self.should_update_prices(data.timestamp) or market_type != "hold":
            self.update_prices(data)

        # 生成交易信号
        return self.generate_signal(data)

    def generate_signal(self, data: MarketData) -> Optional[Signal]:
        """生成交易信号 - 完全对齐qmt_mm.py逻辑"""

        # 0. 14:55清仓检查 (优先级最高)
        if self.position_quantity != 0:
            close_signal = self._check_daily_close(data)
            if close_signal:
                return close_signal

        # 0.5. 持仓时间止损检查 (优先级第二高)
        if self.position_quantity != 0:
            time_stop_signal = self._check_holding_time_stop(data)
            if time_stop_signal:
                return time_stop_signal

        # 1. 止损优先 (sl first)
        if self.position_quantity > 0:
            sl_rate = self.config.sl_ratio  # 可以根据高波动调整
            loss_amount = (self.position_avg_price - data.last_price) * self.position_quantity
            sl_base = self.config.tp_spread * self.config.enter_lot

            if sl_base > 0 and (loss_amount / sl_base) > sl_rate:
                return Signal(
                    action='sell',
                    price=round_price(data.bid_price),
                    quantity=self.position_quantity,
                    reason="止损"
                )

        # 2. 开仓逻辑
        if not self.sell_enable:  # 没有持仓时
            # 如果价格还未更新（buy_price过高），使用市场价格开仓
            if self.buy_price > 100 or data.ask_price <= self.buy_price:
                buy_price = data.ask_price if self.buy_price > 100 else self.buy_price
                return Signal(
                    action='buy',
                    price=round_price(buy_price),
                    quantity=self.config.enter_lot,
                    reason="初始买入"
                )

        # 3. 持仓时的加仓和止盈逻辑
        else:
            exit_price = self.position_avg_price + self.config.tp_spread - 0.0001

            # 网格加仓逻辑
            if self.if_add_vol and self.grid_add_layer < self.config.max_grid_layers:
                reenter_price = self.enter_price
                reenter_lot = self.config.enter_lot

                for i in range(self.grid_add_layer + 1):
                    reenter_price -= self.config.grid_spread * self.config.grid_spreadnet_param[i]
                    reenter_lot = int(self.config.enter_lot * self.config.grid_spreadqty_param[i])

                if data.ask_price <= reenter_price:
                    return Signal(
                        action='buy',
                        price=round_price(reenter_price),
                        quantity=reenter_lot,
                        reason=f"网格加仓第{self.grid_add_layer + 1}层"
                    )

            # 止盈逻辑
            if self.if_add_vol:  # 有加仓的情况
                # 第一次止盈
                if data.bid_price >= exit_price and not self.reach_tp1:
                    tp1_quantity = max(100, (self.position_quantity + 100) // 200 * 100)
                    return Signal(
                        action='sell',
                        price=round_price(data.bid_price),
                        quantity=tp1_quantity,
                        reason="第一次止盈"
                    )

                # 第二次止盈
                elif data.bid_price >= exit_price + self.config.tp_spread and self.reach_tp1:
                    return Signal(
                        action='sell',
                        price=round_price(data.bid_price),
                        quantity=self.position_quantity,
                        reason="第二次止盈"
                    )

                # 继续加仓条件
                elif data.ask_price < self.position_avg_price - self.config.grid_spread:
                    if self.grid_add_layer < self.config.max_grid_layers:
                        reenter_price = self.enter_price
                        reenter_lot = self.config.enter_lot

                        for i in range(self.grid_add_layer + 1):
                            reenter_price -= self.config.grid_spread * self.config.grid_spreadnet_param[i]
                            reenter_lot = int(self.config.enter_lot * self.config.grid_spreadqty_param[i])

                        return Signal(
                            action='buy',
                            price=round_price(reenter_price),
                            quantity=reenter_lot,
                            reason=f"继续加仓第{self.grid_add_layer + 1}层"
                        )

            else:  # 简单止盈 (无加仓状态)
                if data.bid_price >= exit_price:
                    return Signal(
                        action='sell',
                        price=round_price(data.bid_price),
                        quantity=self.position_quantity,
                        reason="简单止盈"
                    )
                # 加仓条件 - 即使在简单模式下也可以加仓
                elif data.ask_price < self.position_avg_price - self.config.grid_spread:
                    if self.grid_add_layer < self.config.max_grid_layers:
                        reenter_price = self.enter_price
                        reenter_lot = self.config.enter_lot

                        for i in range(self.grid_add_layer + 1):
                            reenter_price -= self.config.grid_spread * self.config.grid_spreadnet_param[i]
                            reenter_lot = int(self.config.enter_lot * self.config.grid_spreadqty_param[i])

                        return Signal(
                            action='buy',
                            price=round_price(reenter_price),
                            quantity=reenter_lot,
                            reason=f"初次加仓第{self.grid_add_layer + 1}层"
                        )

        return None

    def on_trade_executed(self, price: float, quantity: int, side: str, timestamp: int) -> None:
        """交易执行回调 - 完全对齐qmt_mm.py逻辑"""
        if side == 'buy':
            # 买入逻辑
            # 记录首次开仓时间
            if self.position_quantity == 0:
                self.position_start_time = timestamp
            if self.position_quantity == 0:
                # 初始买入 - 注意：if_add_vol保持False，只有实际加仓时才设为True
                self.internal_cost = price
                self.enter_price = price
                self.position_quantity = quantity
                self.position_avg_price = price
                self.sell_enable = True
                # self.if_add_vol = False  # 初始买入时保持False
                print(f"初始买入: {quantity}手，价格: {price:.6f}")
            else:
                # 加仓 - 实际加仓时设置if_add_vol = True
                if self.internal_cost > 0:
                    self.internal_cost = (self.position_quantity * self.internal_cost + quantity * price) / (self.position_quantity + quantity)

                self.position_quantity += quantity
                self.position_avg_price = self.internal_cost
                self.grid_add_layer += 1
                self.if_add_vol = True  # 实际加仓时才设为True
                print(f"加仓: {quantity}手，价格: {price:.6f}，当前均价: {self.internal_cost:.6f}")

        elif side == 'sell':
            # 卖出逻辑
            if quantity == self.position_quantity:
                # 全部卖出
                self.position_quantity = 0
                self.position_avg_price = 0
                self.internal_cost = -1.0
                self.enter_price = 0
                self.sell_enable = False
                self.if_add_vol = False
                self.reach_tp1 = False
                self.grid_add_layer = 0
                self.position_start_time = None  # 清除持仓开始时间
                print(f"全部卖出: {quantity}手，价格: {price:.6f}")
            else:
                # 部分卖出（第一次止盈）
                self.position_quantity -= quantity
                self.reach_tp1 = True
                self.grid_add_layer //= 2  # 减少网格层数
                print(f"部分卖出: {quantity}手，价格: {price:.6f}，剩余: {self.position_quantity}手")

    def get_optimization_params(self) -> Dict[str, Tuple[float, float]]:
        """获取优化参数范围"""
        return {
            'enter_lot': (1000, 1000),        # 每次交易数量
            'grid_spread': (0.001, 0.02),    # 0.05%-0.2% 网格价差
            'tp_spread': (0.001, 0.01),      # 0.02%-0.1% 止盈价差
            'sl_ratio': (1, 15.0),            # 1.5-4倍止损比例
            'risk_probility': (0.00001, 0.1),   # AS模型风险概率
            'entropy_ratio_buy_threshold': (1.05, 1.5),
            'entropy_ratio_sell_threshold': (0.5, 0.95),
            'order_update_interval': (3000, 30000),  # 价格更新间隔(毫秒)
            'min_spread': (0.000, 0.5),      # 最小价差
            'max_holding_time_seconds': (300, 5400)  # 30分钟-2小时最大持仓时间
        }

    def update_params(self, params: Dict[str, Any]) -> None:
        """更新策略参数"""
        for key, value in params.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                print(f"🔧 更新参数: {key} = {value}")

    def _check_daily_close(self, data: MarketData) -> Optional[Signal]:
        """检查每日14:55清仓"""
        from datetime import datetime

        # 将毫秒时间戳转换为datetime
        dt = datetime.fromtimestamp(data.timestamp / 1000)

        # 检查是否为交易日的14:55之后到15:00之前
        if (dt.hour == 14 and dt.minute >= 55) or (dt.hour == 15 and dt.minute < 1):
            # 强制清仓
            if self.position_quantity > 0:
                return Signal(
                    action="sell",
                    quantity=self.position_quantity,
                    price=round_price(data.bid_price),
                    reason="Daily close at 14:55 - Long position"
                )
            elif self.position_quantity < 0:
                return Signal(
                    action="buy",
                    quantity=abs(self.position_quantity),
                    price=round_price(data.ask_price),
                    reason="Daily close at 14:55 - Short position"
                )

        return None

    def _check_holding_time_stop(self, data: MarketData) -> Optional[Signal]:
        """检查持仓时间止损"""
        if self.position_start_time is None:
            return None

        # 计算持仓时间（秒）
        holding_time_seconds = (data.timestamp - self.position_start_time) / 1000

        # 检查是否超过最大持仓时间
        if holding_time_seconds > self.config.max_holding_time_seconds:
            # 强制止损
            if self.position_quantity > 0:
                return Signal(
                    action="sell",
                    quantity=self.position_quantity,
                    price=round_price(data.bid_price),
                    reason=f"Holding time stop loss: {holding_time_seconds:.1f}s > {self.config.max_holding_time_seconds:.1f}s"
                )
            elif self.position_quantity < 0:
                return Signal(
                    action="buy",
                    quantity=abs(self.position_quantity),
                    price=round_price(data.ask_price),
                    reason=f"Holding time stop loss: {holding_time_seconds:.1f}s > {self.config.max_holding_time_seconds:.1f}s"
                )

        return None


# 注册QMT策略
strategy_manager.register_strategy('qmt_mm', QMTMMStrategy, QMTMMConfig)
